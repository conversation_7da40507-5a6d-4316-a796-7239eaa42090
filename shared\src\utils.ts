import { RpaStep, ConditionalClickStep, DownloadFileStep, ExtractPdfValuesStep, FortnoxCreateVoucherStep } from './types/steps';

// Base types for validation and API responses
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Utility functions
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function createEmptyFlow(): any {
  return {
    id: generateId(),
    name: '',
    description: '',
    customerId: '',
    steps: [],
    settings: {
      viewport: { width: 1920, height: 1080 },
      timeout: 30000
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }

  const seconds = Math.floor(ms / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
}

// Step creation utilities
export function createStepFromType(stepType: string): RpaStep {
  const baseStep = {
    id: generateId(),
    name: '',
    description: '',
  };

  switch (stepType) {
    case 'navigate':
      return { ...baseStep, type: 'navigate', url: '' }
    case 'click':
      return { ...baseStep, type: 'click', selector: '' }
    case 'fill':
      return { ...baseStep, type: 'fill', selector: '', value: '' }
    case 'type':
      return { ...baseStep, type: 'type', selector: '', text: '' }
    case 'waitForSelector':
      return { ...baseStep, type: 'waitForSelector', selector: '', timeout: 5000 }
    case 'waitForTimeout':
      return { ...baseStep, type: 'waitForTimeout', duration: 1000 }
    case 'extractText':
      return { ...baseStep, type: 'extractText', selector: '', variableName: '' }
    case 'extractAttribute':
      return { ...baseStep, type: 'extractAttribute', selector: '', attribute: 'href', variableName: '' }
    case 'fillPassword':
      return { ...baseStep, type: 'fillPassword', selector: '', credentialId: '' }
    case 'fill2FA':
      return { ...baseStep, type: 'fill2FA', selector: '', credentialId: '' }
    case 'takeScreenshot':
      return { ...baseStep, type: 'takeScreenshot', path: 'screenshot.png' }
    case 'ifElementExists':
      return { ...baseStep, type: 'ifElementExists', selector: '', thenSteps: [], elseSteps: [] }
    case 'conditionalClick':
      return { ...baseStep, type: 'conditionalClick', selector: '', condition: 'exists' }
    case 'downloadFile':
      return { ...baseStep, type: 'downloadFile', triggerSelector: '', filename: '', saveToFile: false }
    case 'extractPdfValues':
      return { ...baseStep, type: 'extractPdfValues', base64Input: '', prompt: 'Extrahera namn, telefonnummer och email från dokumentet' }
    case 'fortnoxCreateVoucher':
      return { ...baseStep, type: 'fortnoxCreateVoucher', description: '', accountMappings: [] }
    default:
      throw new Error(`Unknown step type: ${stepType}`)
  }
}

/**
 * Variable interpolation utilities for RPA flows
 */

/**
 * Interpolates variables in a string using ${variableName} syntax
 * @param text The text containing variable references
 * @param variables The variables object containing values
 * @returns The text with variables replaced by their values
 */
export function interpolateVariables(text: string, variables: Record<string, any>): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Replace ${variableName} with actual values
  return text.replace(/\$\{([^}]+)\}/g, (match, variableName) => {
    const trimmedName = variableName.trim();

    if (trimmedName in variables) {
      const value = variables[trimmedName];
      // Convert to string, handling null/undefined
      return value != null ? String(value) : '';
    }

    // If variable not found, leave the placeholder as is
    return match;
  });
}

/**
 * Checks if a string contains variable references
 * @param text The text to check
 * @returns True if the text contains ${variableName} patterns
 */
export function hasVariableReferences(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }
  return /\$\{[^}]+\}/.test(text);
}

/**
 * Extracts all variable names referenced in a string
 * @param text The text to analyze
 * @returns Array of variable names found in the text
 */
export function extractVariableNames(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const matches = text.match(/\$\{([^}]+)\}/g);
  if (!matches) {
    return [];
  }

  return matches.map(match => {
    const variableName = match.slice(2, -1).trim(); // Remove ${ and }
    return variableName;
  });
}

/**
 * Validates that all variable references in a text can be resolved
 * @param text The text containing variable references
 * @param variables The available variables
 * @returns Object with validation result and missing variables
 */
export function validateVariableReferences(
  text: string,
  variables: Record<string, any>
): { valid: boolean; missingVariables: string[] } {
  const referencedVariables = extractVariableNames(text);
  const missingVariables = referencedVariables.filter(varName => !(varName in variables));

  return {
    valid: missingVariables.length === 0,
    missingVariables
  };
}

// Step label utilities
export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'navigate':
      return `Navigate to: ${step.url}`
    case 'click':
      return `Click: ${step.selector}`
    case 'fill':
      return `Fill: ${step.selector} = "${step.value}"`
    case 'type':
      return `Type: ${step.selector} = "${step.text}"`
    case 'waitForSelector':
      return `Wait for: ${step.selector}`
    case 'waitForTimeout':
      return `Wait: ${step.duration}ms`
    case 'extractText':
      return `Extract text: ${step.selector} → ${step.variableName}`
    case 'extractAttribute':
      return `Extract ${step.attribute}: ${step.selector} → ${step.variableName}`
    case 'fillPassword':
      return `Fill password: ${step.selector}`
    case 'fill2FA':
      return `Fill 2FA: ${step.selector}`
    case 'takeScreenshot':
      return `Take screenshot: ${step.path || 'screenshot.png'}`
    case 'ifElementExists':
      return `If element exists: ${step.selector}`
    case 'conditionalClick':
      const conditionalStep = step as ConditionalClickStep
      return `Conditional click: ${conditionalStep.selector} (if ${conditionalStep.condition})`
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep
      return `Download file: ${downloadStep.triggerSelector || 'trigger'} → ${downloadStep.filename || 'file'}`
    case 'extractPdfValues':
      const extractStep = step as ExtractPdfValuesStep
      return `Extract PDF values: ${extractStep.prompt.substring(0, 50)}...`
    case 'fortnoxCreateVoucher':
      const fortnoxStep = step as FortnoxCreateVoucherStep
      return `Create Fortnox voucher: ${fortnoxStep.description || 'New voucher'}`
    default:
      return step.type
  }
}
